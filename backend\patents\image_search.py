# The main script for image search functionality in the backend of the patent visualizer
# Thin wrapper delegating to shared utils in backend/utils/image_search.py

import os
import numpy as np
import uuid
import time
from typing import List, Dict, Any, Tuple

from qdrant_client.http import models as qdrant_models

from backend.utils.uuid_utils import generate_obfuscated_key
from backend.utils.db_utils import get_patent_data_as_dataframe
from backend.utils.image_search import search_similar_images_generic

COLLECTION_NAME = "IP_Assets_Optimized"
IP_TYPE_FILTER = "Patent"

def get_cos_patent_fig_path(patent_filename):
    """Create IP URL with proper formatting for patent filenames."""
    obfuscated_reg_no = generate_obfuscated_key(os.path.splitext(patent_filename)[0])
    IP_Url = f"https://tro-1330776830.cos.ap-guangzhou.myqcloud.com/ip_assets/Patents/{obfuscated_reg_no}.png"
    return IP_Url

def generate_uuid(num: str) -> str:
    """Generate consistent UUID from filename (PT) using UUID v5."""
    return str(uuid.uuid5(uuid.NAMESPACE_OID, num))

def _parse_qdrant_results_patents(points: List[Dict[str, Any]]) -> Tuple[List[Dict[str, Any]], float]:
    """Domain-specific parsing for Patent results."""
    parsed_results = _parse_initial_qdrant_points(points)
    db_retrieval_time, patent_df = _enrich_results_from_db(parsed_results)
    _attach_patent_info(parsed_results, patent_df)

    return parsed_results, db_retrieval_time


def _parse_initial_qdrant_points(points: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Parses and validates initial Qdrant points."""
    parsed = []

    for point in points:
        payload = point.get("payload") or {}
        reg_no = payload.get("reg_no")
        if not reg_no:
            raise ValueError("The point does not contain a registration number in its payload.")

        maidalv_id = generate_uuid(reg_no)

        parsed.append({
            "Qdrant_id": point["id"],
            "maidalv_patent_table_id": maidalv_id,
            "reg_no": reg_no,
            "score": point["score"],
        })

    return parsed

def _enrich_results_from_db(parsed_results: List[Dict[str, Any]]) -> Tuple[float, Any]:
    """Fetches patent data from DB using parsed maidalv IDs."""
    maidalv_ids = [result["maidalv_patent_table_id"] for result in parsed_results]
    start_time = time.time()
    patent_df = get_patent_data_as_dataframe(maidalv_ids)
    elapsed_time = time.time() - start_time

    if patent_df is None or patent_df.empty:
        raise ValueError("No patent data found for the provided registration numbers.")

    return elapsed_time, patent_df

def _attach_patent_info(parsed_results: List[Dict[str, Any]], patent_df) -> None:
    """Attaches detailed patent info and matched image paths to each result."""
    patent_df['id'] = patent_df['id'].astype(str).str.strip()
    for result in parsed_results:
        target_id = result["maidalv_patent_table_id"]
        match_row = patent_df[patent_df['id'] == target_id]

        if match_row.empty:
            continue

        row = match_row.iloc[0]
        fig_file_names = row.get("fig_files", [])

        result.update({
            "patent_title": row.get("patent_title", ""),
            "date_published": row.get("date_published", ""),
            "fig_file_names": fig_file_names,
            "fig_file_paths": [get_cos_patent_fig_path(fname) for fname in fig_file_names]
        })

        result["matched_image_path"] = _find_matched_figure(result["Qdrant_id"], fig_file_names)

        if not result.get("matched_image_path"):
            raise ValueError(
                f"Matched image not found for Qdrant ID {result['Qdrant_id']} with registration number {result['reg_no']}."
            )

def _find_matched_figure(qdrant_id: str, fig_file_names: List[str]) -> str:
    """Finds and returns the matched figure path based on Qdrant ID."""
    for fig_name in fig_file_names:
        fig_base = os.path.splitext(os.path.basename(fig_name))[0]
        if generate_uuid(fig_base) == qdrant_id:
            return get_cos_patent_fig_path(fig_name)
    return ""

def search_similar_images(to_compare_images: List[str], num_close: int = 10, reg_no_constraint: int = None) -> Dict[str, Any]:
    """Patent-specific image search delegating to generic pipeline."""
    filter_extra = []
    if reg_no_constraint is not None:
        filter_extra.append(
            qdrant_models.FieldCondition(key="reg_no", match=qdrant_models.MatchValue(value=str(reg_no_constraint)))
        )

    return search_similar_images_generic(
        to_compare_images,
        num_close=num_close,
        ip_type_filter=IP_TYPE_FILTER,
        collection_name=COLLECTION_NAME,
        vector_name="siglip_vector",
        filter_extra=filter_extra,
        parse_results_fn=_parse_qdrant_results_patents,
    )

if __name__ == "__main__":
    images_to_compare = [r"C:\Users\<USER>\Downloads\test_image_1.jpg"]
    num_results = 100
    response = search_similar_images(images_to_compare, num_close=num_results)
    results = response["similar_images_info"]
    timings = response["timings"]

    print("\n--- Performance Timings ---")
    print(f"Embedding Conversion Time: {timings['embedding_conversion_sec']:.4f} seconds")
    print(f"Qdrant Retrieval Time: {timings['qdrant_retrieval_sec']:.4f} seconds")
    print(f"Database Retrieval Time: {timings['db_retrieval_sec']:.4f} seconds")
    print("---------------------------\n")

    # for result in results:
    #     print(f"Qdrant ID: {result['Qdrant_id']}, Reg No: {result['reg_no']}, Score: {result['score']}")
    #     print(f"Patent Title: {result['patent_title']}, Date Published: {result['date_published']}")
    #     print(f"Matched Image Path: {result['matched_image_path']}")
    #     print(f"Fig File Names: {result['fig_file_names']}")
    #     print("-" * 40)