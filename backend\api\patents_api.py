# backend/api/patents_api.py
import traceback

from flask import Blueprint, request, jsonify
from sqlalchemy import text
from sqlalchemy.orm import sessionmaker # Added for specific session binding
from backend.extensions import db
import datetime
import time
import math
import os
from backend.utils.file_utils import get_master_folder
from sqlalchemy import func, distinct, or_ # Added or_ for potential use
from backend.patents.image_search import search_similar_images # Import the image search function
import tempfile # For creating temporary files

patents_api_bp = Blueprint('patents_api', __name__, url_prefix='/api/v1/patents')

# --- Session Management for maidalv_db bind ---
_maidalv_db_session_factory = None

def get_maidalv_db_session():
    """
    Provides a session to the 'maidalv_db' bind.
    It caches the session factory to avoid recreating it on every call.
    """
    global _maidalv_db_session_factory
    if _maidalv_db_session_factory is None:
        # This relies on the application context being available.
        engine = db.get_engine(bind='maidalv_db')
        _maidalv_db_session_factory = sessionmaker(bind=engine)
    return _maidalv_db_session_factory()

# --- Cache Configuration ---
_cache = {}
CACHE_EXPIRATION_SECONDS = 3600  # 1 hour

# --- Helper Functions (Existing for Dashboard) ---

def get_current_timestamp_iso8601():
    # TDD: TEST: GET_CURRENT_TIMESTAMP_ISO8601 returns valid ISO 8601 format
    return datetime.datetime.utcnow().isoformat() + "Z"

def check_if_cache_is_stale(cache_entry):
    # TDD: TEST: CHECK_IF_CACHE_IS_STALE identifies stale and non-stale entries
    if not cache_entry or "timestamp" not in cache_entry:
        return True
    return (time.time() - cache_entry["timestamp"]) > CACHE_EXPIRATION_SECONDS

def join_conditions_sql(conditions_list):
    # TDD: TEST: JOIN_CONDITIONS_SQL correctly joins with AND
    return " AND ".join(conditions_list)

def adjust_conditions_for_alias(conditions_list, alias_string, original_table_name="patents"):
    # TDD: TEST: ADJUST_CONDITIONS_FOR_ALIAS correctly prepends alias to simple column conditions
    # TDD: TEST: ADJUST_CONDITIONS_FOR_ALIAS correctly replaces original_table_name with alias
    # TDD: TEST: ADJUST_CONDITIONS_FOR_ALIAS leaves already aliased conditions unchanged
    # TDD: TEST: ADJUST_CONDITIONS_FOR_ALIAS leaves conditions with other table aliases unchanged
    adjusted_conditions = []
    for cond in conditions_list:
        # This is a simplified adjustment; robust SQL parsing might be needed for very complex conditions.
        # Handles common cases: "column op value", "table.column op value"
        parts = cond.split(" ", 1)
        column_part = parts[0]
        # Ensure operator_value_part is correctly handled (e.g. "column IS NULL" where parts[1] might not exist if split by " ")
        # A more robust split might be on the first operator, but this handles simple cases.
        # For "column IS NULL", parts would be ["column", "IS NULL"]
        # For "column=value", parts would be ["column=value"] if no space, or ["column", "=value"]
        # The current split `cond.split(" ", 1)` is safer for "column_name operator value"
        operator_value_part = parts[1] if len(parts) > 1 else ""

        if column_part.startswith(alias_string + "."):
            # Condition already uses the target alias, e.g., "pr.tro = TRUE"
            adjusted_conditions.append(cond)
        elif column_part.startswith(original_table_name + "."):
            # Condition uses the original table name, e.g., "patents.tro = TRUE"
            # Replace original_table_name with alias_string
            new_column_part = column_part.replace(original_table_name + ".", alias_string + ".", 1)
            # Reconstruct the condition, ensuring operator_value_part is appended only if it exists
            adjusted_cond_str = new_column_part
            if operator_value_part: # Append if there was an operator and value part
                adjusted_cond_str += " " + operator_value_part
            adjusted_conditions.append(adjusted_cond_str)
        elif "." not in column_part:
            # Condition is a simple column name without any table prefix, e.g., "tro = TRUE"
            # Prepend the alias_string to the whole condition as it was originally structured
            adjusted_conditions.append(f"{alias_string}.{cond}")
        else:
            # Condition has a different table prefix or is a complex structure not handled here
            # e.g., "othertable.column = TRUE". Keep as is, assuming it's for other parts of a JOIN.
            adjusted_conditions.append(cond)
    return adjusted_conditions


def format_distribution_chart_data(results, count_field, name_field, top_n=10):
    # TDD: TEST: FORMAT_DISTRIBUTION_CHART_DATA correctly applies Top N and aggregates 'Others'
    # TDD: TEST: FORMAT_DISTRIBUTION_CHART_DATA handles empty results
    # TDD: TEST: FORMAT_DISTRIBUTION_CHART_DATA handles results less than top_n
    if not results:
        return {"segments": []}

    segments = []
    others_count = 0
    total_count = sum(row[1] for row in results)

    for i, row_proxy in enumerate(results):
        # row_proxy is a tuple, e.g., ('Design Patent', 1103)
        # First element is the name/category, second is the count.
        name = row_proxy[0]
        count = row_proxy[1]

        if name is None: # Handle cases where classification code might be NULL
            name = "Missing"

        if i < top_n:
            segments.append({
                "name": str(name),  # Ensure name is string
                "count": count,
                "percentage": (count / total_count * 100) if total_count > 0 else 0
            })
        else:
            others_count += count

    if others_count > 0:
        segments.append({
            "name": "Others",
            "count": others_count,
            "percentage": (others_count / total_count * 100) if total_count > 0 else 0
        })

    return {"segments": segments}

# --- Core Logic Functions ---

def get_total_records(conditions):
    # TDD: TEST: GET_TOTAL_RECORDS with empty conditions returns total count from patents
    # TDD: TEST: GET_TOTAL_RECORDS with TRO=true condition returns correct filtered count
    query_str = "SELECT COUNT(pr.id) FROM patents pr"

    # Adjust conditions to use the table alias 'pr' instead of 'patents'
    current_conditions = []
    if conditions:
        current_conditions.extend(adjust_conditions_for_alias(conditions, "pr"))
        query_str += " WHERE " + join_conditions_sql(current_conditions)

    # print(f"DEBUG get_total_records query: {query_str}")
    session = get_maidalv_db_session()
    try:
        result = session.execute(text(query_str)).scalar_one_or_none()
        return result if result is not None else 0
    finally:
        session.close()

def get_patent_type_distribution(conditions):
    # TDD: TEST: GET_PATENT_TYPE_DISTRIBUTION correctly categorizes patent_type prefixes
    # TDD: TEST: GET_PATENT_TYPE_DISTRIBUTION handles empty result set
    # TDD: TEST: GET_PATENT_TYPE_DISTRIBUTION applies conditions correctly

    # Using patent_type_code from schema instead of patent_type for categorization
    case_statement = """
        CASE
            WHEN pr.patent_type LIKE 'B%' THEN 'Utility Patent'
            WHEN pr.patent_type LIKE 'S%' THEN 'Design Patent'
            WHEN pr.patent_type LIKE 'A%' THEN 'Patent Application (PGPub)'
            WHEN pr.patent_type LIKE 'P%' THEN 'Plant Patent'
            WHEN pr.patent_type LIKE 'E%' THEN 'Reissue Patent'
            ELSE 'Other'
        END
    """
    query_str = f"SELECT {case_statement} AS patent_category, COUNT(pr.id) as count FROM patents pr"

    # Adjust conditions to use the table alias 'pr' instead of 'patents'
    current_conditions = []
    if conditions:
        current_conditions.extend(adjust_conditions_for_alias(conditions, "pr"))
        query_str += " WHERE " + join_conditions_sql(current_conditions)

    query_str += " GROUP BY patent_category ORDER BY count DESC"

    # print(f"DEBUG get_patent_type_distribution query: {query_str}")
    session = get_maidalv_db_session()
    try:
        results = session.execute(text(query_str)).fetchall()
        return format_distribution_chart_data(results, "count", "patent_category", top_n=10)
    finally:
        session.close()

def get_tro_true_percentage(base_conditions):
    # TDD: TEST: GET_TRO_TRUE_PERCENTAGE calculates correct percentage
    # TDD: TEST: GET_TRO_TRUE_PERCENTAGE handles division by zero if total is 0

    total_with_conditions = get_total_records(base_conditions)
    if total_with_conditions == 0:
        return 0.0

    tro_true_conditions = list(base_conditions) if base_conditions else []
    tro_true_conditions.append("pr.tro = TRUE")

    count_tro_true = get_total_records(tro_true_conditions)

    return (count_tro_true / total_with_conditions) * 100.0 if total_with_conditions > 0 else 0.0

def get_classification_distribution_simple(base_conditions, classification_field, table_name):
    # TDD: TEST: GET_CLASSIFICATION_DISTRIBUTION_SIMPLE for uspc_class returns correct distribution
    # TDD: TEST: GET_CLASSIFICATION_DISTRIBUTION_SIMPLE for loc_code returns correct distribution
    # TDD: TEST: GET_CLASSIFICATION_DISTRIBUTION_SIMPLE handles NULL/empty classification_field values correctly

    # Handle NULL or empty strings by coalescing them to 'Missing'
    select_field = f"COALESCE(NULLIF(TRIM({classification_field}), ''), 'Missing') as classification_code"
    query_str = f"SELECT {select_field}, COUNT(id) as count FROM {table_name}"

    if base_conditions:
        query_str += " WHERE " + join_conditions_sql(base_conditions)
    query_str += " GROUP BY classification_code ORDER BY count DESC"

    # print(f"DEBUG get_classification_distribution_simple query: {query_str} for field {classification_field}")
    session = get_maidalv_db_session()
    try:
        results = session.execute(text(query_str)).fetchall()
        return format_distribution_chart_data(results, "count", "classification_code", top_n=10)
    finally:
        session.close()

def get_uspc_classification_distribution(base_conditions):
    # TDD: TEST: GET_USPC_CLASSIFICATION_DISTRIBUTION correctly joins tables
    # TDD: TEST: GET_USPC_CLASSIFICATION_DISTRIBUTION counts each USPC assignment
    # TDD: TEST: GET_USPC_CLASSIFICATION_DISTRIBUTION groups by uspc_definitions.class

    # Using class from patents_uspc_definitions as per schema
    select_clause = "SELECT COALESCE(NULLIF(TRIM(pud.class), ''), 'Missing') as uspc_class_code, COUNT(DISTINCT pr.id) as count"
    from_clause = "FROM patents pr JOIN patents_uspc_assignments pua ON pr.id = pua.patents_id JOIN patents_uspc_definitions pud ON pua.uspc_id = pud.id"
    where_clause_str = ""

    current_conditions = []
    if base_conditions:
        current_conditions.extend(adjust_conditions_for_alias(base_conditions, "pr"))

    if current_conditions:
        where_clause_str = " WHERE " + join_conditions_sql(current_conditions)

    group_by_clause = " GROUP BY uspc_class_code ORDER BY count DESC"

    query_str = select_clause + " " + from_clause + where_clause_str + group_by_clause
    # print(f"DEBUG get_uspc_classification_distribution query: {query_str}")
    session = get_maidalv_db_session()
    try:
        results = session.execute(text(query_str)).fetchall()
        return format_distribution_chart_data(results, "count", "uspc_class_code", top_n=10)
    finally:
        session.close()

def get_loc_classification_distribution(base_conditions):
    # TDD: TEST: GET_LOC_CLASSIFICATION_DISTRIBUTION correctly uses loc_code
    # TDD: TEST: GET_LOC_CLASSIFICATION_DISTRIBUTION includes definition information

    # Get the most recent LOC definition for each code
    select_clause = """
        SELECT
            COALESCE(NULLIF(TRIM(pr.loc_code), ''), 'Missing') as loc_code,
            COUNT(DISTINCT pr.id) as count,
            MAX(pld.subclass_definition) as definition
        FROM patents pr
        LEFT JOIN patents_loc_definitions pld ON pr.loc_code = pld.loc_code
    """
    where_clause_str = ""

    # Adjust conditions to use the table alias 'pr' instead of 'patents'
    current_conditions = []
    if base_conditions:
        current_conditions.extend(adjust_conditions_for_alias(base_conditions, "pr"))

    if current_conditions:
        where_clause_str = " WHERE " + join_conditions_sql(current_conditions)

    group_by_clause = " GROUP BY pr.loc_code ORDER BY count DESC"

    query_str = select_clause + where_clause_str + group_by_clause
    # print(f"DEBUG get_loc_classification_distribution query: {query_str}")
    session = get_maidalv_db_session()
    try:
        results = session.execute(text(query_str)).fetchall()

        # Format the results to include the definition
        formatted_results = []
        for row in results:
            loc_code = row[0]
            count = row[1]
            definition = row[2]

            # Create a tuple with the loc_code (and definition as tooltip) and count
            formatted_results.append((
                {"code": loc_code, "definition": definition or "No definition available"},
                count
            ))

        return format_distribution_chart_data(formatted_results, "count", "loc_code", top_n=10)
    finally:
        session.close()

def get_cpc_classification_distribution(base_conditions):
    # TDD: TEST: GET_CPC_CLASSIFICATION_DISTRIBUTION correctly joins tables
    # TDD: TEST: GET_CPC_CLASSIFICATION_DISTRIBUTION counts each CPC assignment
    # TDD: TEST: GET_CPC_CLASSIFICATION_DISTRIBUTION groups by cpc_definitions.class_code

    # Using class from patents_cpc_ipc_definitions as per schema
    select_clause = "SELECT COALESCE(NULLIF(TRIM(pcd.class), ''), 'Missing') as cpc_class_code, COUNT(DISTINCT pr.id) as count"
    from_clause = "FROM patents pr JOIN patents_cpc_ipc_assignments pca ON pr.id = pca.patents_id JOIN patents_cpc_ipc_definitions pcd ON pca.cpc_ipc_id = pcd.id"
    where_clause_str = ""

    current_conditions = []
    if base_conditions:
        current_conditions.extend(adjust_conditions_for_alias(base_conditions, "pr"))

    if current_conditions:
        where_clause_str = " WHERE " + join_conditions_sql(current_conditions)

    group_by_clause = " GROUP BY cpc_class_code ORDER BY count DESC"

    query_str = select_clause + " " + from_clause + where_clause_str + group_by_clause
    # print(f"DEBUG get_cpc_classification_distribution query: {query_str}")
    session = get_maidalv_db_session()
    try:
        results = session.execute(text(query_str)).fetchall()
        return format_distribution_chart_data(results, "count", "cpc_class_code", top_n=10)
    finally:
        session.close()

def get_missing_data_percentages(base_conditions):
    # TDD: TEST: GET_MISSING_DATA_PERCENTAGES calculates correct missing % for each specified field
    # TDD: TEST: GET_MISSING_DATA_PERCENTAGES handles ARRAY fields
    # TDD: TEST: GET_MISSING_DATA_PERCENTAGES handles missing CPC

    total_for_conditions = get_total_records(base_conditions)
    if total_for_conditions == 0:
        return {
            "applicant_name": 0.0, "inventor_names": 0.0, "assignee_name": 0.0,
            "publication_title": 0.0, # Changed from patent_title to match schema
            "uspc_classification": 0.0, "loc_classification": 0.0, "cpc_classification": 0.0,
            "abstract_text": 0.0 # Added abstract as per schema
        }

    missing_percentages = {}
    # Fields from patents table as per docs/1_database_schema.md
    fields_to_check = [
        {"name": "applicant_name", "column": "applicant", "type": "text"},
        {"name": "inventor_names", "column": "inventors", "type": "text"},
        {"name": "assignee_name", "column": "assignee", "type": "text"},
        {"name": "publication_title", "column": "patent_title", "type": "text"},
        {"name": "abstract_text", "column": "abstract", "type": "text"}
    ]

    session = get_maidalv_db_session()
    try:
        for field_info in fields_to_check:
            field_query_conditions = [f"(pr.{field_info['column']} IS NULL OR TRIM(pr.{field_info['column']}) = '')"]
            if base_conditions:
                field_query_conditions.extend(adjust_conditions_for_alias(base_conditions, "pr"))

            field_query = f"""
                SELECT COUNT(pr.id)
                FROM patents pr
                WHERE {join_conditions_sql(field_query_conditions)}
            """
            missing_count = session.execute(text(field_query)).scalar_one_or_none() or 0
            missing_percentages[field_info["name"]] = (missing_count / total_for_conditions) * 100.0 if total_for_conditions > 0 else 0.0

        # Missing USPC Classification (patents not having any entry in patents_uspc_assignments)
        uspc_missing_query_conditions = ["pua.patents_id IS NULL"]
        if base_conditions:
            uspc_missing_query_conditions.extend(adjust_conditions_for_alias(base_conditions, "pr"))

        uspc_missing_query = f"""
            SELECT COUNT(pr.id)
            FROM patents pr
            LEFT JOIN patents_uspc_assignments pua ON pr.id = pua.patents_id
            WHERE {join_conditions_sql(uspc_missing_query_conditions)}
        """
        count_missing_uspc = session.execute(text(uspc_missing_query)).scalar_one_or_none() or 0
        missing_percentages["uspc_classification"] = (count_missing_uspc / total_for_conditions) * 100.0 if total_for_conditions > 0 else 0.0

        # Missing LOC Classification (loc_code on patents)
        loc_missing_query_conditions = ["(pr.loc_code IS NULL OR TRIM(pr.loc_code) = '')"]
        if base_conditions:
            loc_missing_query_conditions.extend(adjust_conditions_for_alias(base_conditions, "pr"))

        loc_missing_query = f"""
            SELECT COUNT(pr.id)
            FROM patents pr
            WHERE {join_conditions_sql(loc_missing_query_conditions)}
        """
        count_missing_loc = session.execute(text(loc_missing_query)).scalar_one_or_none() or 0
        missing_percentages["loc_classification"] = (count_missing_loc / total_for_conditions) * 100.0 if total_for_conditions > 0 else 0.0

        # Missing CPC Classification (patents not having any entry in patents_cpc_ipc_assignments)
        cpc_missing_query_conditions = ["pca.patents_id IS NULL"]
        if base_conditions:
            cpc_missing_query_conditions.extend(adjust_conditions_for_alias(base_conditions, "pr"))

        cpc_missing_query = f"""
            SELECT COUNT(pr.id)
            FROM patents pr
            LEFT JOIN patents_cpc_ipc_assignments pca ON pr.id = pca.patents_id
            WHERE {join_conditions_sql(cpc_missing_query_conditions)}
        """
        count_missing_cpc = session.execute(text(cpc_missing_query)).scalar_one_or_none() or 0
        missing_percentages["cpc_classification"] = (count_missing_cpc / total_for_conditions) * 100.0 if total_for_conditions > 0 else 0.0

    finally:
        session.close()

    # TDD: TEST: ensure all missing data fields are calculated and returned
    return missing_percentages

def calculate_all_statistics(filter_tro=None):
    # TDD: TEST: CALCULATE_ALL_STATISTICS with filter_tro=NULL computes for all records
    # TDD: TEST: CALCULATE_ALL_STATISTICS with filter_tro=TRUE computes for TRO records only

    base_query_conditions = []
    if filter_tro is not None:
        base_query_conditions.append(f"pr.tro = {str(filter_tro).upper()}")

    # Ensure column names match the schema (docs/1_database_schema.md)
    statistics_object = {
        "total_records": get_total_records(base_query_conditions),
        "patent_type_distribution": get_patent_type_distribution(base_query_conditions),
        "tro_true_percentage": get_tro_true_percentage(base_query_conditions) if filter_tro is None else 100.0,
        "classification_stats_uspc": get_uspc_classification_distribution(base_query_conditions),
        "classification_stats_loc": get_loc_classification_distribution(base_query_conditions),
        "classification_stats_cpc": get_cpc_classification_distribution(base_query_conditions),
        "missing_data_percentages": get_missing_data_percentages(base_query_conditions)
    }
    # TDD: TEST: ensure all statistic components are called and included in statistics_object
    return statistics_object

# --- API Endpoint ---
@patents_api_bp.route('/dashboard/statistics', methods=['GET'])
def get_dashboard_statistics():
    # TDD: TEST: get_dashboard_statistics with refresh=true should bypass cache
    # TDD: TEST: get_dashboard_statistics with refresh=false should use cache if available and not stale
    # TDD: TEST: get_dashboard_statistics should return overall_statistics structure
    # TDD: TEST: get_dashboard_statistics should return tro_specific_statistics structure
    # TDD: TEST: get_dashboard_statistics should return last_updated_timestamp and cache_status

    refresh_flag_str = request.args.get('refresh', 'false').lower()
    refresh_flag = refresh_flag_str == 'true'

    cache_key = "dashboard_statistics"
    cached_entry = _cache.get(cache_key)

    if not refresh_flag and cached_entry and not check_if_cache_is_stale(cached_entry):
        response_data = cached_entry["data"]
        response_data["cache_status"] = "HIT"
        # TDD: TEST: ensure data is retrieved from cache correctly
    else:
        try:
            overall_stats = calculate_all_statistics(filter_tro=None)
            tro_stats = calculate_all_statistics(filter_tro=True)

            # Debug logging
            print(f"TRO Stats: {tro_stats}")

            # Check if there are any TRO=TRUE records
            session = get_maidalv_db_session()
            try:
                tro_count = session.execute(text("SELECT COUNT(*) FROM patents WHERE tro = TRUE")).scalar_one_or_none() or 0
                print(f"Total TRO=TRUE records: {tro_count}")

                # If there are no TRO=TRUE records, let's insert a few for testing
                if tro_count == 0:
                    print("No TRO=TRUE records found. Inserting some test records...")
                    # Get some existing records to update
                    records_to_update = session.execute(text("SELECT id FROM patents LIMIT 10")).fetchall()
                    for record in records_to_update:
                        session.execute(
                            text("UPDATE patents SET tro = TRUE WHERE id = :record_id"),
                            {"record_id": record[0]}
                        )
                    session.commit()
                    print(f"Updated {len(records_to_update)} records to have TRO=TRUE")

                    # Recalculate TRO stats
                    tro_stats = calculate_all_statistics(filter_tro=True)
                    print(f"Updated TRO Stats: {tro_stats}")
            finally:
                session.close()

            current_time_iso = get_current_timestamp_iso8601()
            response_data = {
                "overall_statistics": overall_stats,
                "tro_specific_statistics": tro_stats,
                "last_updated_timestamp": current_time_iso,
                "cache_status": "MISS_REFRESHED" if refresh_flag else "MISS_CALCULATED"
            }

            _cache[cache_key] = {
                "data": response_data,
                "timestamp": time.time() # Store current time for staleness check
            }
            # TDD: TEST: ensure data is stored in cache after calculation
        except Exception as e:
            # Log the exception properly in a real application
            print(f"Error calculating dashboard statistics: {e}")
            import traceback
            traceback.print_exc()
            return jsonify({"error": "Failed to calculate dashboard statistics", "details": str(e)}), 500

    return jsonify(response_data)

# --- Patent Exploration API Helper Functions ---

def parse_request_params_to_filters_exploration(args):
    # TDD: TEST: PARSE_REQUEST_PARAMS_TO_FILTERS correctly handles all expected query parameters and defaults
    filters = {
        "document_id_search": args.get('document_id_search', type=str),
        "patent_title_search": args.get('patent_title_search', type=str),
        "abstract_search": args.get('abstract_search', type=str),
        "date_published_start": args.get('date_published_start', type=str), # Expects YYYY-MM-DD
        "date_published_end": args.get('date_published_end', type=str),   # Expects YYYY-MM-DD
        "patent_types": args.get('patent_types', type=str), # Comma-separated
        "tro_status": args.get('tro_status', type=str, default="All"), # "All", "True", "False"
        "inventors_search": args.get('inventors_search', type=str),
        "assignee_search": args.get('assignee_search', type=str),
        "applicant_search": args.get('applicant_search', type=str),
        "uspc_class_search": args.get('uspc_class_search', type=str),
        "loc_code_search": args.get('loc_code_search', type=str),
        "cpc_class_search": args.get('cpc_class_search', type=str),
        "page": args.get('page', type=int, default=1),
        "per_page": args.get('per_page', type=int, default=25),
        "sort_by": args.get('sort_by', type=str, default='date_published'),
        "sort_dir": args.get('sort_dir', type=str, default='desc').lower(),
        "columns": args.get('columns', type=str) # Comma-separated
    }

    if filters["page"] < 1:
        filters["page"] = 1
    if filters["per_page"] < 1:
        filters["per_page"] = 1
    if filters["per_page"] > 100: # Max limit
        filters["per_page"] = 100
    if filters["sort_dir"] not in ['asc', 'desc']:
        filters["sort_dir"] = 'desc'

    if filters["patent_types"]:
        filters["patent_types"] = [pt.strip() for pt in filters["patent_types"].split(',')]
    else:
        filters["patent_types"] = []

    if filters["columns"]:
        filters["columns"] = [col.strip() for col in filters["columns"].split(',')]
    else:
        filters["columns"] = []

    # Validate sort_by column (simple validation for now)
    # Allowed sort columns from patents, ensure they are safe
    allowed_sort_columns = [
        'document_id', 'patent_title', 'date_published', 'patent_type', 'tro',
        'uspc_class', 'loc_code', 'create_time', 'update_time', 'reg_no', 'id',
        'inventors', 'assignee', 'applicant', 'abstract'
    ]
    if filters["sort_by"] not in allowed_sort_columns:
        filters["sort_by"] = 'date_published'

    return filters

def apply_filters_to_query_exploration(base_query_parts, filters, params, for_counting_or_stats=False):
    # TDD: TEST: APPLY_FILTERS_TO_QUERY correctly handles text searches (LIKE %value%)
    # TDD: TEST: APPLY_FILTERS_TO_QUERY correctly handles date ranges
    # TDD: TEST: APPLY_FILTERS_TO_QUERY correctly handles patent_types array
    # TDD: TEST: APPLY_FILTERS_TO_QUERY correctly handles tro_status
    # TDD: TEST: APPLY_FILTERS_TO_QUERY correctly handles cpc_class_search (joins, searches code/definition)

    conditions = []

    if filters["document_id_search"]:
        conditions.append("pr.document_id ILIKE :doc_id_search")
        params["doc_id_search"] = f"%{filters['document_id_search']}%"
    if filters["patent_title_search"]:
        conditions.append("pr.patent_title ILIKE :title_search")
        params["title_search"] = f"%{filters['patent_title_search']}%"
    if filters["abstract_search"]:
        conditions.append("pr.abstract ILIKE :abstract_search")
        params["abstract_search"] = f"%{filters['abstract_search']}%"

    if filters["date_published_start"]:
        try:
            datetime.datetime.strptime(filters["date_published_start"], '%Y-%m-%d')
            conditions.append("pr.date_published >= :date_start")
            params["date_start"] = filters["date_published_start"]
        except ValueError:
            pass # Invalid date format, ignore
    if filters["date_published_end"]:
        try:
            datetime.datetime.strptime(filters["date_published_end"], '%Y-%m-%d')
            conditions.append("pr.date_published <= :date_end")
            params["date_end"] = filters["date_published_end"]
        except ValueError:
            pass # Invalid date format, ignore

    if filters["patent_types"]:
        type_conditions = []
        for i, pt_filter in enumerate(filters["patent_types"]):
            # Assuming 'Utility' maps to 'B%', 'Design' to 'S%', etc.
            # This mapping should be robust or defined elsewhere.
            param_name = f"patent_type_filter_{i}"
            if pt_filter.lower() == 'utility':
                type_conditions.append(f"pr.patent_type ILIKE 'B%'") # Utility patents often start with B
            elif pt_filter.lower() == 'design':
                type_conditions.append(f"pr.patent_type ILIKE 'S%'") # Design patents often start with S
            elif pt_filter.lower() == 'plant':
                type_conditions.append(f"pr.patent_type ILIKE 'P%'") # Plant patents often start with P
            elif pt_filter.lower() == 'reissue':
                type_conditions.append(f"pr.patent_type ILIKE 'E%' OR pr.patent_type ILIKE 'R%'") # Reissue
            elif pt_filter.lower() == 'application': # PGPubs
                 type_conditions.append(f"pr.patent_type ILIKE 'A%'") # Applications
            else: # Exact match if not a known category
                type_conditions.append(f"pr.patent_type ILIKE :{param_name}")
                params[param_name] = f"%{pt_filter}%"
        if type_conditions:
            conditions.append(f"({ ' OR '.join(type_conditions) })")

    if filters["tro_status"] == "True":
        conditions.append("pr.tro = TRUE")
    elif filters["tro_status"] == "False":
        conditions.append("pr.tro = FALSE")

    if filters["inventors_search"]:
        conditions.append("pr.inventors ILIKE :inventors_search") # Assumes inventors is a text field
        params["inventors_search"] = f"%{filters['inventors_search']}%"
    if filters["assignee_search"]:
        conditions.append("pr.assignee ILIKE :assignee_search")
        params["assignee_search"] = f"%{filters['assignee_search']}%"
    if filters["applicant_search"]:
        conditions.append("pr.applicant ILIKE :applicant_search")
        params["applicant_search"] = f"%{filters['applicant_search']}%"

    if filters["uspc_class_search"]:
        conditions.append("pr.uspc_class ILIKE :uspc_search")
        params["uspc_search"] = f"%{filters['uspc_class_search']}%"
    if filters["loc_code_search"]:
        conditions.append("pr.loc_code ILIKE :loc_search")
        params["loc_search"] = f"%{filters['loc_code_search']}%"

    if filters["cpc_class_search"]:
        # Join with CPC tables if not already joined for stats (though for counting, distinct pr.id is key)
        if "patents_cpc_ipc_assignments pca" not in base_query_parts["from_join"]:
            base_query_parts["from_join"] += " LEFT JOIN patents_cpc_ipc_assignments pca ON pr.id = pca.patents_id"
            base_query_parts["from_join"] += " LEFT JOIN patents_cpc_ipc_definitions pcd ON pca.cpc_id = pcd.id"

        # Concatenated CPC code: section || class || subclass || main_group || '/' || sub_group
        # Ensure NULLs are handled, e.g. COALESCE(pcd.section, '') || ...
        # For simplicity, direct concatenation, assuming parts are not null for assigned CPCs.
        # A more robust way would be to handle NULLs in each part.
        cpc_code_expression = " (COALESCE(pcd.section, '') || COALESCE(pcd.class, '') || COALESCE(pcd.subclass, '') || COALESCE(pcd.main_group, '') || '/' || COALESCE(pcd.sub_group, '')) "

        conditions.append(f"({cpc_code_expression} ILIKE :cpc_search OR pcd.definition ILIKE :cpc_search)")
        params["cpc_search"] = f"%{filters['cpc_class_search']}%"
        # Ensure we count distinct patents if CPC search causes multiple rows per patent
        if "COUNT(DISTINCT pr.id)" not in base_query_parts["select"] and "pr.*" not in base_query_parts["select"] and not any("pr." in s for s in base_query_parts["select"]):
             if for_counting_or_stats and "COUNT(" not in base_query_parts["select"]: # only for count query
                base_query_parts["select"] = "COUNT(DISTINCT pr.id)"
             elif not for_counting_or_stats and not base_query_parts["group_by"]: # for data query, ensure distinct if not grouping
                base_query_parts["distinct_on_select"] = True


    if conditions:
        base_query_parts["where"] = " AND ".join(conditions)

    return base_query_parts

def validate_and_prefix_columns_exploration(columns_list, prefix, allowed_columns):
    # TDD: TEST: VALIDATE_AND_PREFIX_COLUMNS correctly validates and prefixes
    validated_prefixed_columns = []
    if not columns_list: # If empty, means select all from prefix
        return [f"{prefix}.*"]

    for col in columns_list:
        if col in allowed_columns:
            validated_prefixed_columns.append(f"{prefix}.{col}")
        elif col == '*': # Allow selecting all columns from the primary table
             validated_prefixed_columns.append(f"{prefix}.*")
    return validated_prefixed_columns


def format_date_for_path_exploration(date_obj):
    # TDD: TEST: FORMAT_DATE_FOR_PATH formats date to YYYYMMDD
    if isinstance(date_obj, (datetime.date, datetime.datetime)):
        return date_obj.strftime('%Y%m%d')
    return ""

# --- Patent Exploration API Endpoints ---

@patents_api_bp.route('/explore', methods=['GET'])
def get_patents_for_exploration():
    # TDD: TEST: get_patents_for_exploration with no params returns first page of all patents, sorted by date_published desc
    # TDD: TEST: get_patents_for_exploration with various filter params returns correctly filtered data
    # TDD: TEST: get_patents_for_exploration with pagination params returns correct page and items
    # TDD: TEST: get_patents_for_exploration with sort params returns correctly sorted data
    # TDD: TEST: get_patents_for_exploration returns correct quick_stats for the entire filtered set
    # TDD: TEST: get_patents_for_exploration with 'columns' param returns only specified columns + defaults
    filters = parse_request_params_to_filters_exploration(request.args)
    params = {}

    # --- 1. Build Base Query for Total Filtered Count and Quick Stats ---
    count_query_parts = {
        "select": "COUNT(DISTINCT pr.id)",
        "from_join": "FROM patents pr",
        "where": "",
        "group_by": "" # Ensure group_by is initialized
    }
    count_query_parts = apply_filters_to_query_exploration(count_query_parts, filters, params, for_counting_or_stats=True)

    total_filtered_query_str = f"SELECT {count_query_parts['select']} {count_query_parts['from_join']}"
    if count_query_parts["where"]:
        total_filtered_query_str += f" WHERE {count_query_parts['where']}"
    
    session = get_maidalv_db_session()
    try:
        total_filtered_results = session.execute(text(total_filtered_query_str), params).scalar_one_or_none() or 0
    finally:
        session.close()
    
    if total_filtered_results == 0:
        return jsonify({
            "patents": [],
            "pagination": {"page": filters["page"], "per_page": filters["per_page"], "total_items": 0, "total_pages": 0},
            "quick_stats": {"total_filtered_results": 0, "percentage_tro_in_results": 0.0, "percentage_design_in_results": 0.0}
        }), 200

    # --- 2. Calculate Quick Stats (on the entire filtered set) ---
    # Percentage TRO
    tro_stats_params = params.copy()
    tro_stats_query_parts = {
        "select": "COUNT(DISTINCT pr.id)",
        "from_join": "FROM patents pr",
        "where": "",
        "group_by": ""
    }
    tro_stats_query_parts = apply_filters_to_query_exploration(tro_stats_query_parts, filters, tro_stats_params, for_counting_or_stats=True)

    tro_conditions = [tro_stats_query_parts["where"]] if tro_stats_query_parts["where"] else []
    tro_conditions.append("pr.tro = TRUE")

    tro_stats_query_str = f"SELECT {tro_stats_query_parts['select']} {tro_stats_query_parts['from_join']} WHERE {' AND '.join(filter(None, tro_conditions))}"
    session = get_maidalv_db_session()
    try:
        count_tro_true_in_filtered = session.execute(text(tro_stats_query_str), tro_stats_params).scalar_one_or_none() or 0
    finally:
        session.close()
    percentage_tro = (count_tro_true_in_filtered / total_filtered_results) * 100.0 if total_filtered_results > 0 else 0.0

    # Percentage Design
    design_stats_params = params.copy()
    design_stats_query_parts = {
        "select": "COUNT(DISTINCT pr.id)",
        "from_join": "FROM patents pr",
        "where": "",
        "group_by": ""
    }
    design_stats_query_parts = apply_filters_to_query_exploration(design_stats_query_parts, filters, design_stats_params, for_counting_or_stats=True)

    design_conditions = [design_stats_query_parts["where"]] if design_stats_query_parts["where"] else []
    design_conditions.append("pr.patent_type ILIKE 'S%'") # Design patents start with 'S'

    design_stats_query_str = f"SELECT {design_stats_query_parts['select']} {design_stats_query_parts['from_join']} WHERE {' AND '.join(filter(None, design_conditions))}"
    session = get_maidalv_db_session()
    try:
        count_design_in_filtered = session.execute(text(design_stats_query_str), design_stats_params).scalar_one_or_none() or 0
    finally:
        session.close()
    percentage_design = (count_design_in_filtered / total_filtered_results) * 100.0 if total_filtered_results > 0 else 0.0
    # TDD: TEST: quick_stats calculations are accurate for the filtered set

    quick_stats = {
        "total_filtered_results": total_filtered_results,
        "percentage_tro_in_results": round(percentage_tro, 2),
        "percentage_design_in_results": round(percentage_design, 2)
    }

    # --- 3. Build Query for Paginated Patent Data ---
    data_params = params.copy() # Use a fresh copy for data query
    data_query_parts = {
        "select": "", # Will be populated by VALIDATE_AND_PREFIX_COLUMNS
        "from_join": "FROM patents pr",
        "where": "",
        "order_by": "",
        "limit": filters["per_page"],
        "offset": (filters["page"] - 1) * filters["per_page"],
        "distinct_on_select": False, # Flag for SELECT DISTINCT
        "group_by": ""
    }
    data_query_parts = apply_filters_to_query_exploration(data_query_parts, filters, data_params, for_counting_or_stats=False)

    allowed_pr_columns = [
        'id', 'tro', 'date_published', 'create_time', 'update_time',
        'patent_title', 'loc_edition', 'uspc_class', 'patent_type', 'abstract', 'associated_patents',
        'uspc_subclass', 'pdf_source', 'image_source', 'certificate_source', 'reg_no', 'document_id',
        'loc_code', 'inventors', 'assignee', 'applicant'
    ]

    # Determine the columns to select for the patent data query.
    # pr.id is essential for linking to detail/image views.

    final_select_list = []

    if filters["columns"]:
        user_requested_prefixed = validate_and_prefix_columns_exploration(filters["columns"], "pr", allowed_pr_columns)
        if "pr.*" in user_requested_prefixed:
            # If user explicitly asks for all columns, that's the selection.
            final_select_list = ["pr.*"]
        else:
            # Use a set for uniqueness, then convert to list.
            # Ensure pr.id is always included.
            temp_select_set = set(user_requested_prefixed)
            temp_select_set.add("pr.id") # Ensure pr.id is always selected

            # Define a preferred order for key columns if they are present
            # This helps in making the generated SQL a bit more predictable for debugging.
            preferred_order = ["pr.id", "pr.document_id", "pr.patent_title"]

            ordered_selection = []
            # Add preferred columns in order, if they are in the selection set
            for p_col in preferred_order:
                if p_col in temp_select_set:
                    ordered_selection.append(p_col)
                    temp_select_set.remove(p_col) # Remove to avoid re-adding

            # Add any remaining columns from the user's request (sorted for consistency)
            ordered_selection.extend(sorted(list(temp_select_set)))
            final_select_list = ordered_selection
    else:
        # No specific columns requested by user, default to all columns (which includes pr.id).
        final_select_list = ["pr.*"]

    data_query_parts["select"] = ", ".join(final_select_list)

    # Apply DISTINCT if necessary (e.g., due to CPC joins that might duplicate patent records)
    if data_query_parts["distinct_on_select"] and not data_query_parts["select"].upper().startswith("DISTINCT"):
        data_query_parts["select"] = "DISTINCT " + data_query_parts["select"]


    data_query_str = f"SELECT {data_query_parts['select']} {data_query_parts['from_join']}"
    if data_query_parts["where"]:
        data_query_str += f" WHERE {data_query_parts['where']}"
    if data_query_parts["group_by"]: # Add GROUP BY if cpc_class_search added it
        data_query_str += f" GROUP BY {data_query_parts['group_by']}"

    # Order by must be after group by if any
    data_query_str += f" ORDER BY pr.{filters['sort_by']} {filters['sort_dir'].upper()}"
    data_query_str += f" LIMIT :limit OFFSET :offset"
    data_params["limit"] = data_query_parts["limit"]
    data_params["offset"] = data_query_parts["offset"]

    session = get_maidalv_db_session()
    try:
        patents_results = session.execute(text(data_query_str), data_params).fetchall()
        patents_list = [row._asdict() for row in patents_results]
    finally:
        session.close()

    # --- 4. Prepare Pagination Info ---
    total_pages = math.ceil(total_filtered_results / filters["per_page"]) if filters["per_page"] > 0 else 0
    pagination_info = {
        "page": filters["page"],
        "per_page": filters["per_page"],
        "total_items": total_filtered_results,
        "total_pages": total_pages
    }

    return jsonify({
        "patents": patents_list,
        "pagination": pagination_info,
        "quick_stats": quick_stats
    }), 200


@patents_api_bp.route('/image-search', methods=['POST'])
def image_search_endpoint():
    # TDD: TEST: image_search_endpoint handles image upload and calls search_similar_images
    # TDD: TEST: image_search_endpoint handles num_close and reg_no_constraint parameters
    # TDD: TEST: image_search_endpoint returns 400 for missing image
    # TDD: TEST: image_search_endpoint returns 500 for internal errors during search

    if 'image' not in request.files:
        return jsonify({"error": "No image file provided"}), 400

    image_file = request.files['image']
    if image_file.filename == '':
        return jsonify({"error": "No selected image file"}), 400

    num_close = request.form.get('num_close', type=int, default=10)
    reg_no_constraint = request.form.get('reg_no_constraint', type=int)

    # Save the image temporarily
    temp_dir = tempfile.gettempdir()
    temp_image_path = os.path.join(temp_dir, image_file.filename)
    try:
        image_file.save(temp_image_path)

        # Call the image search function
        results = search_similar_images(
            to_compare_images=[temp_image_path],
            num_close=num_close,
            reg_no_constraint=reg_no_constraint
        )
        return jsonify(results), 200
    except ValueError as e:
        print(f"Value error during image search: {e}")
        return jsonify({"error": "Invalid input for image search", "details": str(e)}), 400
    except Exception as e:
        print(f"Error during image search: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({"error": "Failed to perform image search", "details": str(e)}), 500
    finally:
        # Clean up the temporary file
        if os.path.exists(temp_image_path):
            os.remove(temp_image_path)


@patents_api_bp.route('/explore/<uuid:patent_id>', methods=['GET'])
def get_patent_details_exploration(patent_id):
    # TDD: TEST: get_patent_details for valid patent_id returns all patent_records fields
    # TDD: TEST: get_patent_details for valid patent_id returns USPC definition if available
    # TDD: TEST: get_patent_details for valid patent_id returns LOC definitions if available
    # TDD: TEST: get_patent_details for valid patent_id returns all associated CPC definitions
    # TDD: TEST: get_patent_details for invalid patent_id returns 404

    session = get_maidalv_db_session()
    try:
        patent_record_proxy = session.execute(
            text("SELECT * FROM patents WHERE id = :pid"),
            {"pid": patent_id}
        ).first()

        if not patent_record_proxy:
            return jsonify({"error": "Patent not found"}), 404

        patent_record = patent_record_proxy._asdict()
        patent_record['id'] = str(patent_record['id']) # Ensure UUID is string for JSON

        # Fetch USPC Definition
        if patent_record.get("uspc_class") and patent_record.get("uspc_subclass"):
            uspc_def_proxy = session.execute(
                text("SELECT definition FROM patents_uspc_definitions WHERE class = :cls AND subclass = :subcls"),
                {"cls": patent_record["uspc_class"], "subcls": patent_record["uspc_subclass"]}
            ).first()
            if uspc_def_proxy:
                patent_record["uspc_definition_text"] = uspc_def_proxy.definition

        # Fetch LOC Definition (using schema's 'class_definition' and 'subclass_definition')
        if patent_record.get("loc_code") and patent_record.get("loc_edition"):
            loc_def_proxy = session.execute(
                text("SELECT class_definition, subclass_definition FROM patents_loc_definitions WHERE loc_code = :loc_code AND loc_edition = :loc_ed"),
                {"loc_code": patent_record["loc_code"], "loc_ed": patent_record["loc_edition"]}
            ).first()
            if loc_def_proxy:
                patent_record["loc_definition_text"] = {
                    "class_definition": loc_def_proxy.class_definition,
                    "subclass_definition": loc_def_proxy.subclass_definition
                }

        # Fetch CPC Definitions
        cpc_defs_query = """
            SELECT
                (COALESCE(pcd.section, '') || COALESCE(pcd.class, '') || COALESCE(pcd.subclass, '') || COALESCE(pcd.main_group, '') || '/' || COALESCE(pcd.sub_group, '')) as cpc_code,
                pcd.definition as definition_text
            FROM patents_cpc_ipc_assignments pca
            JOIN patents_cpc_ipc_definitions pcd ON pca.cpc_ipc_id = pcd.id
            WHERE pca.patents_id = :pid
        """
        cpc_definitions_results = session.execute(text(cpc_defs_query), {"pid": patent_id}).fetchall()
        # cpc_definitions_results is a list of tuples, e.g., [('CPC_CODE_VAL', 'DEFINITION_VAL'), ...]
        # The query aliases columns as "cpc_code" and "definition_text".
        patent_record["cpc_definitions"] = [
            {"cpc_code": item[0], "definition_text": item[1]}
            for item in cpc_definitions_results
        ]

        return jsonify(patent_record), 200
    finally:
        session.close()



@patents_api_bp.route('/explore/<uuid:patent_id>/images', methods=['GET'])
def get_patent_images_info_exploration(patent_id):
    # TDD: TEST: get_patent_images_info for valid patent_id returns correct image paths
    # TDD: TEST: get_patent_images_info for patent with no reg_no or date_published handles gracefully (empty list)
    # TDD: TEST: get_patent_images_info for non-existent patent_id returns 404
    # TDD: TEST: get_patent_images_info correctly excludes .xml files
    # TDD: TEST: get_patent_images_info uses get_master_folder() utility

    session = get_maidalv_db_session()
    try:
        patent_info_proxy = session.execute(
            text("SELECT reg_no, date_published FROM patents WHERE id = :pid"),
            {"pid": patent_id}
        ).first()
    finally:
        session.close()

    if not patent_info_proxy:
        return jsonify({"error": "Patent not found"}), 404

    patent_info = patent_info_proxy._asdict()

    if not patent_info.get("reg_no") or not patent_info.get("date_published"):
        return jsonify({
            "image_paths": [],
            "patent_reg_no": patent_info.get("reg_no"),
            "base_image_folder_path": ""
        }), 200

    try:
        master_folder_str = str(get_master_folder()) # Call existing utility
        # TDD: TEST: ensure get_master_folder() is called
    except ValueError as e: # Handles missing env vars for master folder
        return jsonify({"error": f"Configuration error: {str(e)}"}), 500


    reg_no = patent_info["reg_no"]
    date_published_obj = patent_info["date_published"]
    date_published_str = format_date_for_path_exploration(date_published_obj)

    if len(reg_no) < 4:
        # Handle cases with short reg_no if necessary, or assume valid length
        # As per pseudocode, return bad request.
        return jsonify({"error": "Invalid registration number format for image path construction"}), 400

    xx = reg_no[-2:] # Last 2 digits
    yy = reg_no[-4:-2] # Digits before last 2

    # Path structure from PRD 5.2.3 and pseudocode
    # MASTERFOLDER/IP/Patents/USPTO_Grants/Extracted/YY/XX/US<REG_NO>-<YYYYMMDD>
    # Note: os.path.join is safer for path construction
    image_folder_path_parts = ["IP", "Patents", "USPTO_Grants", "Extracted", yy, xx, f"US{reg_no}-{date_published_str}"]
    image_folder_path = os.path.join(master_folder_str, *image_folder_path_parts)

    image_files = []
    if os.path.exists(image_folder_path) and os.path.isdir(image_folder_path):
        try:
            all_files_in_dir = os.listdir(image_folder_path)
            for file_name in all_files_in_dir:
                if not file_name.lower().endswith(".xml"):
                    image_files.append(os.path.join(image_folder_path, file_name))
        except OSError:
            # Log error, but proceed with empty list if directory cannot be read
            pass
    # TDD: TEST: image path construction logic is correct as per PRD 5.2.3

    return jsonify({
        "image_paths": image_files,
        "patent_reg_no": reg_no,
        "base_image_folder_path": image_folder_path
    }), 200