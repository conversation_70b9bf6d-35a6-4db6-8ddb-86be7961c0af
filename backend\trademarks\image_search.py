# Trademark image search thin wrapper delegating to shared utils in backend/utils/image_search.py

import time
from typing import List, Dict, Any, <PERSON>ple

import pandas as pd
from qdrant_client.http import models as qdrant_models

from backend.utils.uuid_utils import generate_obfuscated_key
from backend.utils.db_utils import get_trademark_data_as_dataframe
from backend.utils.image_search import search_similar_images_generic

COLLECTION_NAME = "IP_Assets_Optimized"
IP_TYPE_FILTER = "Trademark"

def get_cos_trademark_fig_path(ser_no: str) -> str:
    """Create IP URL with proper formatting for trademark images."""
    obfuscated_reg_no = generate_obfuscated_key(ser_no)
    return f"https://tro-1330776830.cos.ap-guangzhou.myqcloud.com/ip_assets/Trademarks/{obfuscated_reg_no}.webp"

def _parse_qdrant_results_trademarks(points: List[Dict[str, Any]]) -> Tuple[List[Dict[str, Any]], float]:
    """Domain-specific parsing for Trademark results."""
    parsed_results, ids = _parse_initial_trademark_points(points)
    db_retrieval_time, trademark_df = _fetch_trademark_data(ids)
    _enrich_trademark_results(parsed_results, trademark_df)

    return parsed_results, db_retrieval_time


def _parse_initial_trademark_points(points: List[Dict[str, Any]]) -> Tuple[List[Dict[str, Any]], List[str]]:
    """Parses initial Qdrant points into result structure and collects IDs."""
    parsed = []
    ids = []

    for point in points:
        point_id = str(point["id"]).strip()
        ids.append(point_id)
        parsed.append({
            "Qdrant_id": point_id,
            "score": point["score"],
        })

    return parsed, ids


def _fetch_trademark_data(ids: List[str]) -> Tuple[float, Any]:
    """Fetches trademark data from DB and normalizes ID column."""
    start_time = time.time()
    trademark_df = get_trademark_data_as_dataframe(ids)
    db_time = time.time() - start_time

    if trademark_df is None or trademark_df.empty:
        raise ValueError("No trademark data found for the provided registration numbers.")

    trademark_df["id"] = trademark_df["id"].astype(str).str.strip()

    return db_time, trademark_df

def _enrich_trademark_results(parsed_results: List[Dict[str, Any]], trademark_df) -> None:
    """Attaches DB info (reg_no, ser_no, etc.) to each result."""
    trademark_df['id'] = trademark_df['id'].astype(str).str.strip()
    for result in parsed_results:
        target_id = result["Qdrant_id"]
        match_row = trademark_df[trademark_df['id'] == target_id]

        if match_row.empty:
            continue

        row = match_row.iloc[0]
        if row is not None:
            result.update({
                "reg_no": row.get("reg_no", ""),
                "ser_no": row.get("ser_no", ""),
                "Filing_date": row.get("filing_date", ""),
                "matched_image_path": get_cos_trademark_fig_path(row.get("ser_no", "")),
            })

def search_similar_images(to_compare_images: List[str], num_close: int = 10, reg_no_constraint: int = None) -> Dict[str, Any]:
    """Trademark-specific image search delegating to generic pipeline."""
    filter_extra = []
    if reg_no_constraint is not None:
        filter_extra.append(
            qdrant_models.FieldCondition(key="reg_no", match=qdrant_models.MatchValue(value=str(reg_no_constraint)))
        )

    return search_similar_images_generic(
        to_compare_images,
        num_close=num_close,
        ip_type_filter=IP_TYPE_FILTER,
        collection_name=COLLECTION_NAME,
        vector_name="siglip_vector",
        filter_extra=filter_extra,
        parse_results_fn=_parse_qdrant_results_trademarks,
    )

if __name__ == "__main__":
    images_to_compare = [r"C:\Users\<USER>\Downloads\test_trademark_image_1.png"]
    num_results = 100
    response = search_similar_images(images_to_compare, num_close=num_results)
    results = response["similar_images_info"]
    timings = response["timings"]

    print("\n--- Performance Timings ---")
    print(f"Embedding Conversion Time: {timings['embedding_conversion_sec']:.4f} seconds")
    print(f"Qdrant Retrieval Time: {timings['qdrant_retrieval_sec']:.4f} seconds")
    print(f"Database Retrieval Time: {timings['db_retrieval_sec']:.4f} seconds")
    print("---------------------------\n")