import os
import tempfile

from flask import Blueprint

from backend.api.patents_api import format_date_for_path_exploration
from backend.database.trademark_model import Trademark
from backend.extensions import db
from backend.trademarks.image_search import get_cos_trademark_fig_path, search_similar_images
from flask import request, jsonify
from datetime import datetime

from backend.utils.file_utils import get_master_folder
from backend.utils.uuid_utils import generate_obfuscated_key

trademarks_api_bp = Blueprint('trademarks_api', __name__, url_prefix='/api/v1/trademarks')

@trademarks_api_bp.route('/image-search', methods=['POST'])
def image_search_endpoint():
    if 'image' not in request.files:
        return jsonify({"error": "No image file provided"}), 400

    image_file = request.files['image']
    if image_file.filename == '':
        return jsonify({"error": "No selected image file"}), 400

    num_close = request.form.get('num_close', type=int, default=10)
    reg_no_constraint = request.form.get('reg_no_constraint', type=int)

    # Save the uploaded image to a temporary file
    temp_dir = tempfile.gettempdir()
    temp_image_path = os.path.join(temp_dir, image_file.filename)
    image_file.save(temp_image_path)

    try:
        results = search_similar_images(
            to_compare_images=[temp_image_path],
            num_close=num_close,
            reg_no_constraint=reg_no_constraint
        )
        return jsonify(results), 200
    except ValueError as e:
        return jsonify({"error": str(e)}), 400
    except Exception as e:
        return jsonify({"error": f"An internal error occurred: {str(e)}"}), 500
    finally:
        # Clean up the temporary file
        if os.path.exists(temp_image_path):
            os.remove(temp_image_path)

@trademarks_api_bp.route('/explore', methods=['GET'])
def get_trademarks_for_exploration():
    try:
        page = request.args.get('page', default=1, type=int)
        per_page = request.args.get('per_page', default=20, type=int)

        query = db.session.query(Trademark)

        # Example filters
        reg_no = request.args.get('reg_no')
        ser_no = request.args.get('ser_no')
        mark_text = request.args.get('mark_text')
        applicant_name = request.args.get('applicant_name')
        filing_date_start = request.args.get('filing_date_start')  # expect YYYY-MM-DD
        filing_date_end = request.args.get('filing_date_end')  # expect YYYY-MM-DD
        mark_feature_code = request.args.get('mark_feature_code')
        mark_current_status_code = request.args.get('mark_current_status_code')
        plaintiff_id = request.args.get('plaintiff_id')
        tro = request.args.get('tro')

        if reg_no:
            query = query.filter(Trademark.reg_no.ilike(f"%{reg_no}%"))
        if ser_no:
            query = query.filter(Trademark.ser_no.ilike(f"%{ser_no}%"))
        if mark_text:
            query = query.filter(Trademark.mark_text.ilike(f"%{mark_text}%"))
        if applicant_name:
            query = query.filter(Trademark.applicant_name.ilike(f"%{applicant_name}%"))

        from datetime import datetime

        if filing_date_start:
            try:
                # Parse ISO format string and extract the date part
                start_date = datetime.strptime(filing_date_start, '%Y-%m-%dT%H:%M:%S.%fZ').date()
                query = query.filter(Trademark.filing_date >= start_date)
            except ValueError as e:
                print(f"Invalid date format for filing_date_start: {e}")

        if filing_date_end:
            try:
                end_date = datetime.strptime(filing_date_end, '%Y-%m-%dT%H:%M:%S.%fZ').date()
                query = query.filter(Trademark.filing_date <= end_date)
            except ValueError as e:
                print(f"Invalid date format for filing_date_end: {e}")

        if mark_feature_code:
            query = query.filter(Trademark.mark_feature_code == mark_feature_code)

        if mark_current_status_code:
            query = query.filter(Trademark.mark_current_status_code == mark_current_status_code)

        if tro:
            if tro == "True":
                query = query.filter(Trademark.tro.is_(True))
            elif tro == "False":
                query = query.filter(Trademark.tro.is_(False))

        if plaintiff_id:
            query = query.filter(Trademark.plaintiff_id == plaintiff_id)

        total = query.count()

        trademarks = query.offset((page - 1) * per_page).limit(per_page).all()

        trademarks_serialized = [
            {c.name: getattr(tm, c.name) for c in tm.__table__.columns}
            for tm in trademarks
        ]

        return jsonify({
            "trademarks": trademarks_serialized,
            "total": total,
            "page": page,
            "per_page": per_page,
        }), 200
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@trademarks_api_bp.route('/explore/<int:trademark>/images', methods=['GET'])
def get_trademark_images_info_exploration(trademark):
    reg_no = str(trademark)
    if not reg_no:
        return jsonify({"error": "Trademark not found"}), 404

    if not reg_no:
        return jsonify({
            "image_path": "",
        }), 200
    xx = reg_no[-2:] # Last 2 digits
    yy = reg_no[-4:-2] # Digits before last 2
    try:
        master_folder_str = str(get_master_folder()) # Call existing utility
        # TDD: TEST: ensure get_master_folder() is called
    except ValueError as e: # Handles missing env vars for master folder
        return jsonify({"error": f"Configuration error: {str(e)}"}), 500

    if len(reg_no) < 4:
        # Handle cases with short reg_no if necessary, or assume valid length
        # As per pseudocode, return bad request.l̥
        return jsonify({"error": "Invalid registration number format for image path construction"}), 400

    # MASTERFOLDER/IP/Trademark/Images/reg_no.webp
    # Note: os.path.join is safer for path construction
    image_folder_path_parts = ["IP", "Trademark", "USPTO_Daily",xx,yy]
    image_folder_path = os.path.join(master_folder_str, *image_folder_path_parts)
    image_file = f"{reg_no}.webp"
    return jsonify({
        "image_path": get_cos_trademark_fig_path(str(trademark)),
    }), 200

def get_cos_trademark_cert_path(ser_no: str) -> str:
    """Create IP URL with proper formatting for trademark certificate."""
    obfuscated_reg_no = generate_obfuscated_key(ser_no)
    return f"https://tro-1330776830.cos.ap-guangzhou.myqcloud.com/ip_assets/Trademarks/Certificate/{obfuscated_reg_no}.webp"

@trademarks_api_bp.route('/explore/<uuid:trademark_id>', methods=['GET'])
def get_trademark_details_exploration(trademark_id):
    trademark = db.session.query(Trademark).filter(Trademark.id == trademark_id).first()
    
    try:
        master_folder_str = str(get_master_folder()) # Call existing utility
        # TDD: TEST: ensure get_master_folder() is called
    except ValueError as e: # Handles missing env vars for master folder
        return jsonify({"error": f"Configuration error: {str(e)}"}), 500
    
    
    xx = trademark.reg_no[-2:] # Last 2 digits
    yy = trademark.reg_no[-4:-2] # Digits before last 2
    
    # Construct the path relative to the master folder for directory scanning
    image_folder_path_parts = ["IP", "Trademark", "USPTO_Daily", xx, yy]
    absolute_image_folder_path = os.path.join(master_folder_str, *image_folder_path_parts)

    # This should be the base URL for serving static files, not the file system path
    base_url_path = f"/static/{'/'.join(image_folder_path_parts)}"

    return jsonify({
        'reg_no': trademark.reg_no,
        'ser_no': trademark.ser_no,
        'applicant_name': trademark.applicant_name,
        'mark_text': trademark.mark_text,
        'int_cls': trademark.int_cls,
        'filing_date': trademark.filing_date.isoformat() if trademark.filing_date else None,
        'plaintiff_id': trademark.plaintiff_id,
        'nb_suits': trademark.nb_suits,
        'mark_current_status_code': trademark.mark_current_status_code,
        'mark_feature_code': trademark.mark_feature_code,
        'tro':trademark.tro,
        'certificate':{
            "image_file_paths": get_cos_trademark_cert_path(trademark.ser_no),
        }
    }), 200