# backend/utils/image_search.py
# Shared helpers for image search across IP types (Patents, Trademarks)
# Provides: embedding generation with lazy Siglip model, Qdrant querying, result parsing hooks, and orchestration.

import os
import time
import uuid
from typing import Callable, List, Tuple, Dict, Any, Optional

import numpy as np
from qdrant_client.http import models as qdrant_models

from backend.AI.shared_models import get_siglip_model
from backend.utils.vector_store import get_qdrant_client

# Initialize QdrantClient globally using the shared function
_qdrant_client_instance = get_qdrant_client()

# Embedding helpers

def siglip_embedding(image_path: str) -> np.ndarray[np.float32]:
    """
    Generate a single image embedding using the lazy Siglip model.
    """
    model = get_siglip_model(load_if_needed=True)
    return model.compute_features(data_list=[image_path], data_type="image")[0]

# Qdrant helpers

def query_qdrant_by_embedding(
    query_embedding: np.ndarray[np.float32],
    collection_name: str,
    vector_name: str,
    num_close: int,
    filter_must: Optional[List[qdrant_models.FieldCondition]] = None,
) -> List[Dict[str, Any]]:
    """
    Query Qdrant by embedding with optional filter conditions.
    Returns a list of entries with id, score, and payload (if requested in config).
    """
    must_conditions = filter_must or []
    query_filter = qdrant_models.Filter(must=must_conditions) if must_conditions else None

    query_response = _qdrant_client_instance.query_points(
        collection_name=collection_name,
        query=query_embedding,
        using=vector_name,
        limit=num_close,
        query_filter=query_filter,
        with_payload=True,
        with_vectors=False,
    )

    results = []
    for result in query_response.points:
        results.append(
            {
                "id": result.id,
                "score": result.score,
                "payload": getattr(result, "payload", None),
            }
        )
    return results

# Orchestration

def search_similar_images_generic(
    to_compare_images: List[str],
    *,
    num_close: int,
    ip_type_filter: str,
    collection_name: str = "IP_Assets_Optimized",
    vector_name: str = "siglip_vector",
    filter_extra: Optional[List[qdrant_models.FieldCondition]] = None,
    parse_results_fn: Callable[[List[Dict[str, Any]]], Tuple[List[Dict[str, Any]], float]] = lambda points: (points, 0.0),
) -> Dict[str, Any]:
    """
    Generic image search pipeline:
      1) Compute embedding with Siglip
      2) Query Qdrant with ip_type filter and any extra conditions
      3) Parse results via provided parse_results_fn (domain-specific)
      4) Return structured results with timing metrics

    parse_results_fn must return: (parsed_results: List[dict], db_retrieval_time_sec: float)
    """
    if len(to_compare_images) != 1:
        raise ValueError("Currently only one image is supported for comparison.")

    # 1) Embedding
    start_time_embedding = time.time()
    query_embedding = siglip_embedding(to_compare_images[0])
    embedding_conversion_time = time.time() - start_time_embedding

    # 2) Build filters
    must_conditions: List[qdrant_models.FieldCondition] = [
        qdrant_models.FieldCondition(key="ip_type", match=qdrant_models.MatchValue(value=ip_type_filter))
    ]
    if filter_extra:
        must_conditions.extend(filter_extra)

    # 3) Qdrant query
    start_time_qdrant = time.time()
    points = query_qdrant_by_embedding(
        query_embedding=query_embedding,
        collection_name=collection_name,
        vector_name=vector_name,
        num_close=num_close,
        filter_must=must_conditions,
    )
    qdrant_retrieval_time = time.time() - start_time_qdrant

    # 4) Domain parsing
    parsed_results, db_retrieval_time = parse_results_fn(points)

    return {
        "similar_images_info": parsed_results,
        "timings": {
            "embedding_conversion_sec": embedding_conversion_time,
            "qdrant_retrieval_sec": qdrant_retrieval_time,
            "db_retrieval_sec": db_retrieval_time,
        },
    }